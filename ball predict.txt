-- Ball Prediction

local Players = game:GetService("Players")
local RunService = game:GetService("RunService")
local Workspace = game:GetService("Workspace")

local LocalPlayer = Players.LocalPlayer

local CONFIG = {
    ENABLED = true,
    PREDICTION_TIME = 4,
    PREDICTION_STEPS = 25,
    TRAIL_THICKNESS = 0.4,
    TRAIL_COLOR = Color3.fromRGB(0, 255, 255),
    WALL_HIT_COLOR = Color3.fromRGB(255, 50, 50),
    IMPACT_COLOR = Color3.fromRGB(255, 100, 100),
    UPDATE_RATE = 0.1,
    MIN_VELOCITY = 2,
    RAYCAST_DISTANCE = 15,
    WALL_DETECTION_SENSITIVITY = 0.8
}

local ActiveBalls = {}
local PredictionParts = {}
local TrailConnection
local LastUpdateTime = 0
local LastBallScanTime = 0
local BALL_SCAN_INTERVAL = 1.5
local GoalAreas = {}

local RaycastParams = RaycastParams.new()
RaycastParams.FilterType = Enum.RaycastFilterType.Blacklist
RaycastParams.FilterDescendantsInstances = {}

local function FindGoalkeeperAreas()
    local areas = {}
    local searchPaths = {
        workspace:FindFirstChild("WorkspaceStadiumMap"),
        workspace:FindFirstChild("StadiumMap"),
        workspace:FindFirstChild("Stadium"),
        workspace:FindFirstChild("Map"),
        workspace
    }
    
    for _, container in ipairs(searchPaths) do
        if container then
            local gkNames = {"GK1", "GK2", "GoalArea1", "GoalArea2", "Gol1", "Gol2", "Goal1", "Goal2"}
            
            for _, gkName in ipairs(gkNames) do
                local gkArea = container:FindFirstChild(gkName)
                if gkArea and gkArea:IsA("BasePart") then
                    table.insert(areas, {
                        name = gkName,
                        part = gkArea,
                        position = gkArea.Position,
                        size = gkArea.Size
                    })
                end
            end
            
            for _, child in ipairs(container:GetChildren()) do
                if child:IsA("Folder") or child:IsA("Model") then
                    for _, gkName in ipairs({"GK1", "GK2", "GoalArea1", "GoalArea2", "Gol1", "Gol2", "Goal1", "Goal2"}) do
                        local gkArea = child:FindFirstChild(gkName)
                        if gkArea and gkArea:IsA("BasePart") then
                            table.insert(areas, {
                                name = gkName,
                                part = gkArea,
                                position = gkArea.Position,
                                size = gkArea.Size
                            })
                        end
                    end
                end
            end
        end
    end
    
    return areas
end

local function FindAllTPSBalls()
    local balls = {}
    local searchContainers = {
        workspace,
        workspace:FindFirstChild("WorkspaceLeaderboards"),
        workspace:FindFirstChild("Leaderboards"),
        workspace:FindFirstChild("Balls"),
        workspace:FindFirstChild("Game"),
        workspace:FindFirstChild("GameObjects")
    }
    
    local function searchInContainer(container, depth)
        if not container or depth > 3 then return end
        
        for _, obj in ipairs(container:GetChildren()) do
            if obj.Name == "TPS" and obj:IsA("BasePart") then
                table.insert(balls, obj)
                if not table.find(RaycastParams.FilterDescendantsInstances, obj) then
                    table.insert(RaycastParams.FilterDescendantsInstances, obj)
                end
            end
            
            if obj:IsA("Folder") or obj:IsA("Model") then
                searchInContainer(obj, depth + 1)
            end
        end
    end
    
    for _, container in ipairs(searchContainers) do
        if container then
            searchInContainer(container, 0)
        end
    end
    
    return balls
end

local function IsInGoalkeeperArea(position)
    if #GoalAreas == 0 then
        GoalAreas = FindGoalkeeperAreas()
    end
    
    for _, area in ipairs(GoalAreas) do
        if area.part and area.part.Parent then
            local areaPos = area.part.Position
            local areaSize = area.part.Size
            local areaMin = areaPos - areaSize/2
            local areaMax = areaPos + areaSize/2
            
            if position.X >= areaMin.X and position.X <= areaMax.X and 
               position.Y >= areaMin.Y and position.Y <= areaMax.Y and 
               position.Z >= areaMin.Z and position.Z <= areaMax.Z then
                return true, area.name
            end
        end
    end
    
    return false, nil
end

local function DetectObstacleCollision(currentPos, velocity, deltaTime)
    local inGKArea, areaName = IsInGoalkeeperArea(currentPos)
    if inGKArea then
        return nil
    end
    
    if velocity.Magnitude < 0.1 then return nil end
    
    local direction = velocity.Unit
    local distance = velocity.Magnitude * deltaTime * 2
    local maxDistance = math.min(distance, CONFIG.RAYCAST_DISTANCE)
    
    local raycastResult = workspace:Raycast(currentPos, direction * maxDistance, RaycastParams)
    
    if raycastResult then
        local hitPart = raycastResult.Instance
        local hitPosition = raycastResult.Position
        local hitNormal = raycastResult.Normal
        
        if hitPart and hitPart.CanCollide and hitPart ~= workspace.Terrain then
            local distanceToHit = (hitPosition - currentPos).Magnitude
            
            if distanceToHit <= velocity.Magnitude * deltaTime * CONFIG.WALL_DETECTION_SENSITIVITY then
                return {
                    position = hitPosition,
                    normal = hitNormal,
                    part = hitPart,
                    isWallHit = true
                }
            end
        end
    end
    
    return nil
end

local function CalculateWallBounce(velocity, normal, bounciness)
    bounciness = bounciness or 0.7
    local reflectedVel = velocity - 2 * velocity:Dot(normal) * normal
    return reflectedVel * bounciness
end

local function ClearBallPredictions(ballId)
    if PredictionParts[ballId] then
        for _, part in ipairs(PredictionParts[ballId]) do
            if part and part.Parent then
                part:Destroy()
            end
        end
        PredictionParts[ballId] = nil
    end
end

local function ClearAllPredictions()
    for ballId, _ in pairs(PredictionParts) do
        ClearBallPredictions(ballId)
    end
    PredictionParts = {}
end

local function CreatePredictionPoint(ballId, position, size, color, transparency)
    local point = Instance.new("Part")
    point.Name = "BallPrediction_" .. ballId
    point.Anchored = true
    point.CanCollide = false
    point.Shape = Enum.PartType.Ball
    point.Material = Enum.Material.Neon
    point.Size = Vector3.new(size, size, size)
    point.Position = position
    point.Color = color
    point.Transparency = transparency or 0.3
    point.Parent = workspace
    
    local pointLight = Instance.new("PointLight")
    pointLight.Color = color
    pointLight.Brightness = 0.5
    pointLight.Range = 3
    pointLight.Parent = point
    
    if not PredictionParts[ballId] then
        PredictionParts[ballId] = {}
    end
    
    table.insert(PredictionParts[ballId], point)
    return point
end

local function CreateTrajectoryLine(ballId, startPos, endPos, color, thickness, isWallHit)
    local distance = (endPos - startPos).Magnitude
    if distance < 0.05 then return end
    
    local midPoint = (startPos + endPos) / 2
    
    local line = Instance.new("Part")
    line.Name = "TrajectoryLine_" .. ballId
    line.Anchored = true
    line.CanCollide = false
    line.Material = Enum.Material.Neon
    line.Color = color
    line.Transparency = isWallHit and 0.1 or 0.2
    line.Size = Vector3.new(thickness, thickness, distance)
    line.CFrame = CFrame.lookAt(midPoint, endPos)
    line.Parent = workspace
    
    local surfaceLight = Instance.new("SurfaceLight")
    surfaceLight.Color = color
    surfaceLight.Brightness = isWallHit and 1 or 0.5
    surfaceLight.Range = 2
    surfaceLight.Face = Enum.NormalId.Front
    surfaceLight.Parent = line
    
    if not PredictionParts[ballId] then
        PredictionParts[ballId] = {}
    end
    
    table.insert(PredictionParts[ballId], line)
    return line
end

local function PredictBallTrajectory(startPos, startVel)
    local predictions = {}
    local currentPos = startPos
    local currentVel = startVel
    local deltaTime = CONFIG.PREDICTION_TIME / CONFIG.PREDICTION_STEPS
    local gravity = Vector3.new(0, -workspace.Gravity, 0)
    local inGoalkeeperArea = false
    local currentAreaName = nil
    
    for i = 1, CONFIG.PREDICTION_STEPS do
        local currentlyInGKArea, areaName = IsInGoalkeeperArea(currentPos)
        if currentlyInGKArea and not inGoalkeeperArea then
            inGoalkeeperArea = true
            currentAreaName = areaName
        end
        
        local isWallHit = false
        local isGroundHit = false
        
        if not inGoalkeeperArea then
            local obstacleHit = DetectObstacleCollision(currentPos, currentVel, deltaTime)
            
            if obstacleHit then
                currentPos = obstacleHit.position
                currentVel = CalculateWallBounce(currentVel, obstacleHit.normal, 0.8)
                isWallHit = true
            else
                local drag = currentVel * -0.003
                currentVel = currentVel + (gravity + drag) * deltaTime
                local nextPos = currentPos + (currentVel * deltaTime)
                
                if nextPos.Y < currentPos.Y then
                    local groundRay = workspace:Raycast(currentPos, Vector3.new(0, -100, 0), RaycastParams)
                    if groundRay and nextPos.Y <= groundRay.Position.Y + 1 then
                        nextPos = Vector3.new(nextPos.X, groundRay.Position.Y + 1, nextPos.Z)
                        currentVel = Vector3.new(currentVel.X * 0.8, -currentVel.Y * 0.6, currentVel.Z * 0.8)
                        isGroundHit = true
                    end
                end
                
                currentPos = nextPos
            end
        else
            local drag = currentVel * -0.003
            currentVel = currentVel + (gravity + drag) * deltaTime
            local nextPos = currentPos + (currentVel * deltaTime)
            
            if nextPos.Y < currentPos.Y then
                local groundRay = workspace:Raycast(currentPos, Vector3.new(0, -100, 0))
                if groundRay and nextPos.Y <= groundRay.Position.Y + 1 then
                    nextPos = Vector3.new(nextPos.X, groundRay.Position.Y + 1, nextPos.Z)
                    currentVel = Vector3.new(currentVel.X * 0.8, -currentVel.Y * 0.5, currentVel.Z * 0.8)
                    isGroundHit = true
                end
            end
            
            currentPos = nextPos
        end
        
        table.insert(predictions, {
            position = currentPos,
            velocity = currentVel,
            isWallHit = isWallHit,
            isGroundHit = isGroundHit,
            isImpact = isWallHit or isGroundHit,
            inGoalkeeperArea = inGoalkeeperArea,
            areaName = currentAreaName
        })
        
        if currentVel.Magnitude < 0.3 or currentPos.Y < -100 then
            break
        end
    end
    
    return predictions
end

local function DrawBallPrediction(ballId, predictions)
    ClearBallPredictions(ballId)
    
    if not predictions or #predictions == 0 then return end
    
    local skipRate = math.max(1, math.floor(#predictions / 15))
    
    for i = 1, #predictions, skipRate do
        local pred = predictions[i]
        local size = 0.6
        local transparency = 0.2 + (i / #predictions) * 0.4
        local color = CONFIG.TRAIL_COLOR
        
        if pred.isWallHit then
            color = CONFIG.WALL_HIT_COLOR
            size = 0.9
            transparency = 0.1
        elseif pred.isGroundHit then
            color = CONFIG.IMPACT_COLOR
            size = 0.8
            transparency = 0.2
        end
        
        if pred.inGoalkeeperArea then
            color = Color3.fromRGB(255, 255, 0)
            transparency = 0.3
        end
        
        CreatePredictionPoint(ballId, pred.position, size, color, transparency)
        
        if i > skipRate then
            local prevIndex = math.max(1, i - skipRate)
            local lineColor = pred.isWallHit and CONFIG.WALL_HIT_COLOR or 
                            (pred.inGoalkeeperArea and Color3.fromRGB(255, 255, 0) or CONFIG.TRAIL_COLOR)
            CreateTrajectoryLine(ballId, predictions[prevIndex].position, pred.position, lineColor, CONFIG.TRAIL_THICKNESS, pred.isWallHit)
        end
    end
    
    if #predictions > 0 then
        local finalPoint = predictions[#predictions]
        local finalColor = finalPoint.inGoalkeeperArea and Color3.fromRGB(255, 255, 0) or CONFIG.IMPACT_COLOR
        CreatePredictionPoint(ballId, finalPoint.position, 1.2, finalColor, 0.05)
    end
end

local function UpdateBallPrediction(ball, ballId)
    if not ball or not ball.Parent then
        ClearBallPredictions(ballId)
        ActiveBalls[ballId] = nil
        return
    end
    
    local currentPos = ball.Position
    local currentVel = ball.AssemblyLinearVelocity or ball.Velocity or Vector3.new(0, 0, 0)
    
    if currentVel.Magnitude > CONFIG.MIN_VELOCITY then
        local predictions = PredictBallTrajectory(currentPos, currentVel)
        DrawBallPrediction(ballId, predictions)
        ActiveBalls[ballId].active = true
    elseif ActiveBalls[ballId] and ActiveBalls[ballId].active then
        ClearBallPredictions(ballId)
        ActiveBalls[ballId].active = false
    end
end

local function UpdatePredictions()
    if not CONFIG.ENABLED then return end
    
    local currentTime = tick()
    
    if currentTime - LastUpdateTime < CONFIG.UPDATE_RATE then
        return
    end
    LastUpdateTime = currentTime
    
    if currentTime - LastBallScanTime > BALL_SCAN_INTERVAL then
        LastBallScanTime = currentTime
        
        local foundBalls = FindAllTPSBalls()
        local newActiveBalls = {}
        
        for _, ball in ipairs(foundBalls) do
            local ballId = tostring(ball)
            newActiveBalls[ballId] = {
                ball = ball,
                active = ActiveBalls[ballId] and ActiveBalls[ballId].active or false
            }
        end
        
        for ballId, _ in pairs(ActiveBalls) do
            if not newActiveBalls[ballId] then
                ClearBallPredictions(ballId)
            end
        end
        
        ActiveBalls = newActiveBalls
    end
    
    for ballId, ballData in pairs(ActiveBalls) do
        UpdateBallPrediction(ballData.ball, ballId)
    end
end

local function Cleanup()
    if TrailConnection then
        TrailConnection:Disconnect()
        TrailConnection = nil
    end
    ClearAllPredictions()
    ActiveBalls = {}
    GoalAreas = {}
end

local function Initialize()
    GoalAreas = FindGoalkeeperAreas()
    
    local foundBalls = FindAllTPSBalls()
    for _, ball in ipairs(foundBalls) do
        local ballId = tostring(ball)
        ActiveBalls[ballId] = {
            ball = ball,
            active = false
        }
    end
    
    TrailConnection = RunService.Heartbeat:Connect(UpdatePredictions)
end

game.Players.PlayerRemoving:Connect(function(player)
    if player == LocalPlayer then
        Cleanup()
    end
end)

Initialize()