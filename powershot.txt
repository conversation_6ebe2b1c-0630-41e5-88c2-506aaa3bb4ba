"-- powertshot
local Players = game:GetService("Players")
local UserInputService = game:GetService("UserInputService")
local RunService = game:GetService("RunService")
local TweenService = game:GetService("TweenService")

local LocalPlayer = Players.LocalPlayer

local euamohentai = {
EnableCurve = true,
ShootPower = 280,
ShootDuration = 1.2,
ArcHeight = 70,
CurveAmount = 45,
LowArcHeight = 10,
ActivationKey = Enum.KeyCode.RightBracket
}

local isLuxyShooting = false
local luxy2 = nil

local function CubicBezier(t, p0, p1, p2, p3)
return (1-t)^3p0 + 3(1-t)^2tp1 + 3*(1-t)t^2p2 + t^3*p3
end

local function QuadraticBezier(t, p0, p1, p2)
return (1-t)^2p0 + 2(1-t)tp1 + t^2*p2
end

local function Luxy1()
if isLuxyShooting or not luxy2 then return end

Generated code
local character = LocalPlayer.Character
if not character or not character:FindFirstChild("HumanoidRootPart") then return end

isLuxyShooting = true

local tps = luxy2
tps.Anchored = false

local trail = tps:FindFirstChild("PowerShootTrail") or Instance.new("Trail")
trail.Parent = tps
trail.Name = "PowerShootTrail"
trail.Color = ColorSequence.new(Color3.fromRGB(0, 255, 127), Color3.fromRGB(0, 100, 50))
trail.Lifetime = 0.3
trail.Enabled = true

local att0 = trail.Attachment0 or Instance.new("Attachment", tps)
att0.Name = "Attachment0"
trail.Attachment0 = att0

local att1 = trail.Attachment1 or Instance.new("Attachment", tps)
att1.Name = "Attachment1"
trail.Attachment1 = att1

local particles = tps:FindFirstChild("PowerShootParticles") or Instance.new("ParticleEmitter", tps)
particles.Name = "PowerShootParticles"
particles.Color = ColorSequence.new(Color3.fromRGB(0, 255, 127))
particles.Rate = 80
particles.Enabled = true

local camera = workspace.CurrentCamera
local rootPart = character.HumanoidRootPart
local startPos = tps.Position
local direction = camera.CFrame.LookVector
local endPos = rootPart.Position + (direction * euamohentai.ShootPower)

local attachment = tps:FindFirstChild("PS_Attach") or Instance.new("Attachment", tps)
attachment.Name = "PS_Attach"

local alignPosition = Instance.new("AlignPosition", tps)
alignPosition.Attachment0 = attachment
alignPosition.Mode = Enum.PositionAlignmentMode.OneAttachment
alignPosition.MaxForce = 120000
alignPosition.MaxVelocity = euamohentai.ShootPower * 1.5
alignPosition.Responsiveness = 200

local elapsedTime = 0
local connection
connection = RunService.Heartbeat:Connect(function(deltaTime)
    elapsedTime = elapsedTime + deltaTime
    local t = math.min(elapsedTime / euamohentai.ShootDuration, 1)
    local newPosition

    if euamohentai.EnableCurve then
        local upVector = Vector3.new(0, euamohentai.ArcHeight, 0)
        local sideVector = direction:Cross(Vector3.yAxis) * euamohentai.CurveAmount
        local controlPoint1 = startPos + (direction * (euamohentai.ShootPower * 0.25)) + upVector
        local midPoint = startPos + (direction * (euamohentai.ShootPower * 0.75))
        local controlPoint2 = midPoint + (upVector * 0.7) + sideVector
        newPosition = CubicBezier(t, startPos, controlPoint1, controlPoint2, endPos)
    else
        local controlPoint = ((startPos + endPos) / 2) + Vector3.new(0, euamohentai.LowArcHeight, 0)
        newPosition = QuadraticBezier(t, startPos, controlPoint, endPos)
    end

    if alignPosition and alignPosition.Parent then
        alignPosition.Position = newPosition
    else
        tps.CFrame = CFrame.new(newPosition)
    end

    if t >= 1 then
        connection:Disconnect()
        trail.Enabled = false
        particles.Enabled = false
        if alignPosition then alignPosition:Destroy() end
        if attachment then attachment:Destroy() end
        isLuxyShooting = false
    end
end)
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
IGNORE_WHEN_COPYING_END

end

task.spawn(function()
while true do
local foundBall = nil
for _, descendant in ipairs(workspace:GetDescendants()) do
if descendant.Name == "TPS" and descendant:IsA("BasePart") then
local ownerValue = descendant:FindFirstChild("Owner")
if ownerValue and ownerValue:IsA("ObjectValue") and ownerValue.Value == LocalPlayer then
foundBall = descendant
break
end
end
end
luxy2 = foundBall
task.wait(0.2)
end
end)

pcall(function() game:GetService("Players").LocalPlayer.PlayerGui.PowerShootGUI:Destroy() end)
local LuxyGUI = Instance.new("ScreenGui")
LuxyGUI.Name = "PowerShootGUI"
LuxyGUI.ResetOnSpawn = false
LuxyGUI.Parent = LocalPlayer:WaitForChild("PlayerGui")

local LuxyButton = Instance.new("TextButton")
LuxyButton.Name = "PowerShootButton"
LuxyButton.Parent = LuxyGUI
LuxyButton.Text = "POWERSHOT"
LuxyButton.Font = Enum.Font.SourceSansBold
LuxyButton.TextSize = 24
LuxyButton.TextColor3 = Color3.fromRGB(255, 255, 255)
LuxyButton.BackgroundColor3 = Color3.fromRGB(20, 20, 20)
LuxyButton.Size = UDim2.new(0, 120, 0, 50)
LuxyButton.Position = UDim2.new(0.85, 0, 0.7, 0)

local corner = Instance.new("UICorner")
corner.Parent = LuxyButton
corner.CornerRadius = UDim.new(0, 8)

local stroke = Instance.new("UIStroke")
stroke.Parent = LuxyButton
stroke.Color = Color3.fromRGB(0, 255, 127)
stroke.Thickness = 1.5

LuxyButton.MouseEnter:Connect(function()
TweenService:Create(LuxyButton, TweenInfo.new(0.2), {BackgroundColor3 = Color3.fromRGB(50, 50, 50)}):Play()
end)

LuxyButton.MouseLeave:Connect(function()
TweenService:Create(LuxyButton, TweenInfo.new(0.2), {BackgroundColor3 = Color3.fromRGB(20, 20, 20)}):Play()
end)

LuxyButton.MouseButton1Click:Connect(Luxy1)

local isDragging = false
local dragOffset = Vector2.new()

LuxyButton.InputBegan:Connect(function(input)
if input.UserInputType == Enum.UserInputType.MouseButton1 or input.UserInputType == Enum.UserInputType.Touch then
isDragging = true
local inputPosition = Vector2.new(input.Position.X, input.Position.Y)
dragOffset = LuxyButton.AbsolutePosition - inputPosition
return Enum.ContextActionResult.Sink
end
end)

UserInputService.InputChanged:Connect(function(input)
if isDragging and (input.UserInputType == Enum.UserInputType.MouseMovement or input.UserInputType == Enum.UserInputType.Touch) then
local inputPosition = Vector2.new(input.Position.X, input.Position.Y)
local newPosition = inputPosition + dragOffset
LuxyButton.Position = UDim2.fromOffset(newPosition.X, newPosition.Y)
end
end)

UserInputService.InputEnded:Connect(function(input)
if input.UserInputType == Enum.UserInputType.MouseButton1 or input.UserInputType == Enum.UserInputType.Touch then
isDragging = false
end
end)

UserInputService.InputBegan:Connect(function(input, gameProcessedEvent)
if gameProcessedEvent then return end
if input.KeyCode == euamohentai.ActivationKey then
Luxy1()
end
end)"